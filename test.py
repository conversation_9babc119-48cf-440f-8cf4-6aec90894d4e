from NodeRAG import NodeConfig, NodeSearch
import pandas as pd
import re

# Load configuration from the main folder
config = NodeConfig.from_main_folder(fr"/tmp2/b11705045/AI/Final_project")

# Initialize search engine
search = NodeSearch(config)

with open('baseline_prompt.txt', 'r') as file:
    prompt = file.read()

# Read the query data
df = pd.read_csv("final_project_query.csv")

# Prepare results list
results = []

# Process each row (testing with first 3 rows only)
for i, row in df.head(3).iterrows():
    # Get the answer from NodeRAG
    ans = search.answer(f'{prompt}\n請分析廣告內容：\n{row["Question"]}')

    # Extract violation probability from response
    response_text = ans.response
    print(f"Processing ID {row['ID']}: {response_text}")

    # Use regex to find violation probability percentage
    # Look for patterns like "違法機率: 90%" or "違法機率: 10%"
    probability_match = re.search(r'違法機率[：:]\s*(\d+)%', response_text)

    if probability_match:
        probability = int(probability_match.group(1))
        # If probability >= 80%, output 1, otherwise 0
        answer = 1 if probability >= 80 else 0
    else:
        # If we can't find the probability, default to 0 (safe assumption)
        answer = 0
        print(f"Warning: Could not extract probability from response for ID {row['ID']}")

    results.append({
        'ID': row['ID'] - 1,  # Adjust ID to match template (0-based)
        'Answer': answer
    })

# Create output DataFrame and save to CSV
output_df = pd.DataFrame(results)
output_df.to_csv('task2.csv', index=False)

print(f"Processing complete. Results saved to task2.csv")
print(f"Total processed: {len(results)} items")